<?php
/**
 * Plugin Name: GPT Assistant
 * Plugin URI:  https://example.com/elementor-gpt-site-assistant
 * Description: A powerful AI-Powered Chat Assistant deeply integrated with Elementor, allowing site owners to train the AI on their website's content.
 * Version:     1.0.0
 * Author:      Your Name
 * Author URI:  https://example.com
 * License:     GPL-2.0+
 * License URI: http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain: elementor-gpt-site-assistant
 * Domain Path: /languages
 */

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

final class GPT_Site_Assistant_Plugin {

    /**
     * Plugin Version
     *
     * @since 1.0.0
     * @var string The plugin version.
     */
    const VERSION = '1.0.0';

    /**
     * Minimum Elementor Version
     *
     * @since 1.0.0
     * @var string Minimum Elementor version required to run the plugin.
     */
    const MINIMUM_ELEMENTOR_VERSION = '3.0.0';

    /**
     * Minimum PHP Version
     *
     * @since 1.0.0
     * @var string Minimum PHP version required to run the plugin.
     */
    const MINIMUM_PHP_VERSION = '7.4';

    /**
     * Instance
     *
     * @since 1.0.0
     * @access private
     * @static
     * @var GPT_Site_Assistant_Plugin The single instance of the class.
     */
    private static $_instance = null;

    /**
     * Instance
     *
     * Ensures only one instance of the class is loaded or can be loaded.
     *
     * @since 1.0.0
     * @access public
     * @static
     * @return GPT_Site_Assistant_Plugin An instance of the class.
     */
    public static function instance() {
        if ( is_null( self::$_instance ) ) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor
     *
     * @since 1.0.0
     * @access public
     */
    public function __construct() {
        add_action( 'plugins_loaded', [ $this, 'on_plugins_loaded' ] );
    }

    /**
     * On Plugins Loaded
     *
     * Checks if Elementor and required PHP version are met.
     *
     * @since 1.0.0
     * @access public
     */
    public function on_plugins_loaded() {
        if ( ! $this->check_requirements() ) {
            add_action( 'admin_notices', [ $this, 'admin_notice_requirements_met' ] );
            return;
        }

        $this->includes();
        $this->init_hooks();
    }

    /**
     * Check Requirements
     *
     * Checks if the plugin meets all of its requirements.
     *
     * @since 1.0.0
     * @access private
     * @return bool True if all requirements are met, false otherwise.
     */
    private function check_requirements() {
        if ( version_compare( PHP_VERSION, self::MINIMUM_PHP_VERSION, '<' ) ) {
            return false;
        }

        if ( ! did_action( 'elementor/loaded' ) ) {
            return false;
        }

        if ( version_compare( ELEMENTOR_VERSION, self::MINIMUM_ELEMENTOR_VERSION, '<' ) ) {
            return false;
        }

        return true;
    }

    /**
     * Admin Notice
     *
     * Warning when the site doesn't meet the plugin requirements.
     *
     * @since 1.0.0
     * @access public
     */
    public function admin_notice_requirements_met() {
        $message = sprintf(
            /* translators: 1: Plugin name 2: Elementor 3: Required Elementor version 4: PHP 5: Required PHP version */
            esc_html__( '"%1$s" requires "%2$s" version %3$s or greater, and "%4$s" version %5$s or greater.', 'elementor-gpt-site-assistant' ),
            '<strong>' . esc_html__( 'Elementor AI Chat Assistant', 'elementor-gpt-site-assistant' ) . '</strong>',
            '<strong>' . esc_html__( 'Elementor', 'elementor-gpt-site-assistant' ) . '</strong>',
            self::MINIMUM_ELEMENTOR_VERSION,
            '<strong>' . esc_html__( 'PHP', 'elementor-gpt-site-assistant' ) . '</strong>',
            self::MINIMUM_PHP_VERSION
        );

        printf( '<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message );
    }

    /**
     * Include Files
     *
     * Load the required files for the plugin.
     *
     * @since 1.0.0
     * @access private
     */
    private function includes() {
        require_once __DIR__ . '/includes/helpers.php';
        require_once __DIR__ . '/includes/class-gpt-assistant-settings.php';
        require_once __DIR__ . '/includes/class-gpt-assistant-rest.php';
        require_once __DIR__ . '/includes/class-gpt-assistant-shortcode.php';
    }

    /**
     * Initialize Hooks
     *
     * @since 1.0.0
     * @access private
     */
    private function init_hooks() {
        add_action( 'elementor/widgets/register', [ $this, 'register_widgets' ] );
        add_action( 'wp_enqueue_scripts', [ $this, 'enqueue_frontend_scripts' ] );
        add_action( 'admin_enqueue_scripts', [ $this, 'enqueue_admin_scripts' ] );
        add_action( 'admin_menu', [ GPT_Site_Assistant_Plugin_Settings::instance(), 'add_admin_menu' ] );
        add_action( 'rest_api_init', [ GPT_Site_Assistant_Plugin_REST::instance(), 'register_routes' ] );
        add_shortcode( 'ai_chat_assistant', [ GPT_Site_Assistant_Plugin_Shortcode::instance(), 'render_shortcode' ] );

        // Register activation and deactivation hooks
        register_activation_hook( __FILE__, [ $this, 'activate' ] );
        register_deactivation_hook( __FILE__, [ $this, 'deactivate' ] );
    }

    /**
     * Register Widgets
     *
     * Register new Elementor widgets.
     *
     * @since 1.0.0
     * @access public
     * @param \Elementor\Widgets_Manager $widgets_manager Elementor widgets manager.
     */
    public function register_widgets( $widgets_manager ) {
        require_once __DIR__ . '/widgets/class-gpt-assistant-widget.php';
        $widgets_manager->register( new \GPT_Site_Assistant_Widget() );
    }

    /**
     * Enqueue Frontend Scripts
     *
     * Enqueue scripts and styles for the frontend.
     *
     * @since 1.0.0
     * @access public
     */
    public function enqueue_frontend_scripts() {
        wp_enqueue_style(
            'elementor-gpt-site-assistant-frontend',
            plugins_url( 'assets/css/frontend.css', __FILE__ ),
            [],
            self::VERSION
        );

        wp_enqueue_script(
            'elementor-gpt-site-assistant-frontend',
            plugins_url( 'assets/js/frontend.js', __FILE__ ),
            [ 'jquery' ],
            self::VERSION,
            true
        );

        wp_localize_script(
            'elementor-gpt-site-assistant-frontend',
            'aiChatAssistant',
            [
                'ajax_url' => admin_url( 'admin-ajax.php' ), // For shortcode fallback
                'rest_url' => get_rest_url( null, 'ai-chat/v1/query' ),
                'nonce'    => wp_create_nonce( 'wp_rest' ),
            ]
        );
    }

    /**
     * Enqueue Admin Scripts
     *
     * Enqueue scripts and styles for the admin area.
     *
     * @since 1.0.0
     * @access public
     */
    public function enqueue_admin_scripts() {
        wp_enqueue_style(
            'elementor-gpt-site-assistant-admin',
            plugins_url( 'assets/css/admin.css', __FILE__ ),
            [],
            self::VERSION
        );

        wp_enqueue_script(
            'elementor-gpt-site-assistant-admin',
            plugins_url( 'assets/js/admin.js', __FILE__ ),
            [ 'jquery' ],
            self::VERSION,
            true
        );
    }

    /**
     * Activate the plugin.
     *
     * @since 1.0.0
     * @access public
     */
    public function activate() {
        // Placeholder for activation tasks, e.g., setting default options.
    }

    /**
     * Deactivate the plugin.
     *
     * @since 1.0.0
     * @access public
     */
    public function deactivate() {
        // Placeholder for deactivation tasks.
    }
}

GPT_Site_Assistant_Plugin::instance();

// Include uninstall script
if ( file_exists( __DIR__ . '/uninstall.php' ) ) {
    require_once __DIR__ . '/uninstall.php';
}
