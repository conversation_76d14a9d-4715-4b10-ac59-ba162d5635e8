/* Elementor AI Chat Assistant - Admin Styles */
.ai-chat-admin-wrap {
  max-width: 900px;
  margin: 30px auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  padding: 32px 28px;
}
.ai-chat-admin-title {
  font-size: 1.6em;
  margin-bottom: 18px;
  font-weight: 600;
}
.ai-chat-admin-tabs {
  display: flex;
  gap: 14px;
  margin-bottom: 24px;
}
.ai-chat-admin-tab {
  background: #f5f5f5;
  border: none;
  border-radius: 6px 6px 0 0;
  padding: 10px 22px;
  cursor: pointer;
  font-size: 1.1em;
  color: #333;
  outline: none;
  transition: background 0.2s;
}
.ai-chat-admin-tab.active {
  background: #3f51b5;
  color: #fff;
}
.ai-chat-admin-section {
  display: none;
}
.ai-chat-admin-section.active {
  display: block;
}
.ai-chat-admin-field {
  margin-bottom: 22px;
}
.ai-chat-admin-label {
  font-weight: 500;
  margin-bottom: 6px;
  display: block;
}
.ai-chat-admin-input,
.ai-chat-admin-textarea {
  width: 100%;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 1em;
  margin-bottom: 2px;
  background: #fafbfc;
  color: #222;
}
.ai-chat-admin-textarea {
  min-height: 120px;
  resize: vertical;
}
.ai-chat-admin-desc {
  color: #666;
  font-size: 0.98em;
  margin-bottom: 10px;
}
.ai-chat-admin-save {
  background: #3f51b5;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 10px 28px;
  font-size: 1.1em;
  cursor: pointer;
  margin-top: 8px;
  transition: background 0.2s;
}
.ai-chat-admin-save:disabled {
  opacity: 0.6;
  cursor: default;
}
