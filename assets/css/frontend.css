/* Elementor AI Chat Assistant - Frontend Styles */
:root {
  --ai-chat-accent: linear-gradient(135deg, #00f260 0%, #0575e6 100%);
  --ai-chat-header-bg: rgba(15, 15, 15, 0.95);
  --ai-chat-header-color: #fff;
  --ai-chat-bot-bg: rgba(255,255,255,0.04);
  --ai-chat-user-bg: rgba(255,255,255,0.08);
  --ai-chat-bot-color: #fff;
  --ai-chat-user-color: #fff;
  --ai-chat-window-width: 380px;
  --ai-chat-window-height: 600px;
  --ai-chat-border-radius: 20px;
}

.ai-chat-bubble {
  position: fixed;
  bottom: 32px;
  right: 32px;
  background: var(--ai-chat-accent);
  color: #fff;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0,0,0,0.18);
  cursor: pointer;
  z-index: 9999;
  transition: background 0.2s;
}
.ai-chat-bubble:hover {
  background: #283593;
}

.ai-chat-window {
  position: fixed;
  bottom: 100px;
  right: 32px;
  width: var(--ai-chat-window-width);
  max-width: 95vw;
  height: var(--ai-chat-window-height);
  max-height: 92vh;
  background: rgba(15, 15, 15, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.08);
  border-radius: var(--ai-chat-border-radius);
  box-shadow: 0 20px 40px rgba(0,0,0,0.3);
  display: flex;
  flex-direction: column;
  z-index: 10000;
  overflow: hidden;
  font-family: inherit;
  animation: ai-slideIn 0.5s ease-out;
}
@keyframes ai-slideIn {
  from { opacity: 0; transform: translateY(20px) scale(0.98); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}

.ai-chat-header {
  background: none;
  color: var(--ai-chat-header-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid rgba(255,255,255,0.06);
}
.ai-chat-header .ai-icon {
  width: 20px;
  height: 20px;
  background: rgba(255,255,255,0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  margin-right: 10px;
  color: #fff;
}
.ai-chat-header .title {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  letter-spacing: -0.01em;
}
.ai-chat-header .ai-chat-close {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: rgba(255,255,255,0.5);
  font-size: 18px;
}
.ai-chat-header .ai-chat-close:hover {
  background: rgba(255,255,255,0.08);
  color: rgba(255,255,255,0.8);
}

.ai-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 32px 24px 12px 24px;
  background: none;
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.ai-chat-message {
  display: flex;
  align-items: flex-end;
  gap: 10px;
}

.ai-chat-message.bot {
  flex-direction: row;
}
.ai-chat-message.user {
  flex-direction: row-reverse;
}

.ai-chat-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #eee;
  object-fit: cover;
}

.ai-chat-bubble-content {
  padding: 12px 18px;
  border-radius: 14px;
  font-size: 1em;
  max-width: 80%;
  word-break: break-word;
  background: rgba(255,255,255,0.04);
  color: #fff;
  border: 1px solid rgba(255,255,255,0.08);
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.ai-chat-message.bot .ai-chat-bubble-content {
  background: var(--ai-chat-bot-bg);
  color: var(--ai-chat-bot-color);
}
.ai-chat-message.user .ai-chat-bubble-content {
  background: var(--ai-chat-user-bg);
  color: var(--ai-chat-user-color);
}

.ai-chat-typing {
  font-style: italic;
  color: #888;
  margin: 6px 0 0 48px;
  font-size: 0.95em;
}

.ai-chat-input-row {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 24px;
  background: none;
  border-top: 1px solid rgba(255,255,255,0.06);
}

.ai-chat-input {
  flex: 1;
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 10px;
  padding: 12px 14px;
  font-size: 13px;
  outline: none;
  background: rgba(255,255,255,0.03);
  color: #fff;
  font-weight: 400;
  transition: all 0.2s ease;
}
.ai-chat-input:focus {
  border-color: rgba(255,255,255,0.2);
  background: rgba(255,255,255,0.05);
}
.ai-chat-input::placeholder {
  color: rgba(255,255,255,0.4);
}

.ai-chat-send-btn {
  background: var(--ai-chat-accent);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  font-size: 1em;
  cursor: pointer;
  transition: background 0.2s;
}
.ai-chat-send-btn:disabled {
  opacity: 0.6;
  cursor: default;
}

@media (max-width: 600px) {
  .ai-chat-window {
    width: 97vw;
    height: 90vh;
    right: 1vw;
    bottom: 1vh;
    border-radius: 12px;
  }
  .ai-chat-bubble {
    right: 3vw;
    bottom: 3vw;
  }
}
