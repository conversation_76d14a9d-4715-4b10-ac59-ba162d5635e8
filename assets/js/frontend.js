/* Elementor AI Chat Assistant - Frontend JS */
(function($){
  let chatOpen = false;
  let history = [];
  let typingTimeout = null;
  let settings = window.aiChatAssistantSettings || {};

  function renderChatBubble() {
    if ($('.ai-chat-bubble').length) return;
    $('body').append('<div class="ai-chat-bubble" aria-label="Open chat" tabindex="0"><svg width="28" height="28" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="14" cy="14" r="14" fill="white" fill-opacity=".13"/><path d="M7 11.5C7 9.01472 9.01472 7 11.5 7H16.5C18.9853 7 21 9.01472 21 11.5V16.5C21 18.9853 18.9853 21 16.5 21H11.5C9.01472 21 7 18.9853 7 16.5V11.5Z" fill="currentColor"/></svg></div>');
  }

  function renderChatWindow() {
    if ($('.ai-chat-window').length) return;
    const header = settings.headerText || 'AI Assist';
    const botName = settings.botName || 'Support Bot';
    const greeting = settings.greeting || 'Hello! How can I assist you today?';
    const inputPlaceholder = settings.inputPlaceholder || 'Ask me anything...';
    // Modern glassy UI markup
    const chatWindow = $(`
      <div class="ai-chat-window" style="display:none;">
        <div class="ai-chat-header">
          <div class="header-left">
            <div class="ai-icon">AI</div>
            <span class="title">${header}</span>
          </div>
          <button class="ai-chat-close" aria-label="Close chat">&times;</button>
        </div>
        <div class="main-content" style="display:none;">
          <div class="orb-container">
            <div class="animated-orb" id="ai-orb"></div>
          </div>
          <div class="question-text">What do you want to know</div>
          <div class="subtitle">about this website?</div>
          <div class="suggestions">
            <button class="suggestion-btn">Generate Summary</button>
            <button class="suggestion-btn">Are they a good fit for my job post?</button>
            <button class="suggestion-btn">What is their training style?</button>
          </div>
          <button class="show-more">Show more</button>
        </div>
        <div class="ai-chat-messages"></div>
        <div class="ai-chat-typing" style="display:none;">${botName} is typing...</div>
        <form class="ai-chat-input-row" autocomplete="off">
          <button class="topics-btn" type="button"><span>⚡</span><span>Topics</span></button>
          <input type="text" class="ai-chat-input" placeholder="${inputPlaceholder}" autocomplete="off" required />
          <button class="ai-chat-send-btn send-btn" type="submit">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M22 2L11 13M22 2l-7 20-4-9-9-4 20-7z"/></svg>
          </button>
        </form>
      </div>
    `);
    $('body').append(chatWindow);
    // Show main content (orb and suggestions) only on first open
  }

  function showChatWindow() {
    $('.ai-chat-window').fadeIn(200);
    chatOpen = true;
    if (history.length === 0) {
      // Show orb and suggestions if no history
      $('.ai-chat-window .main-content').show();
      $('.ai-chat-messages').hide();
      $('.ai-chat-typing').hide();
    } else {
      $('.ai-chat-window .main-content').hide();
      $('.ai-chat-messages').show();
      $('.ai-chat-typing').hide();
      renderHistory();
    }
  }

  // Handle suggestion click
  $(document).on('click', '.ai-chat-window .suggestion-btn', function() {
    const text = $(this).text();
    $('.ai-chat-window .main-content').hide();
    $('.ai-chat-messages').show();
    $('.ai-chat-typing').hide();
    addMessage('user', text);
    sendMessage(text);
  });

  // Orb click animation (optional, for effect)
  $(document).on('click', '#ai-orb', function() {
    $(this).css('transform', 'scale(0.95)');
    setTimeout(() => {
      $('#ai-orb').css('transform', '');
    }, 150);
  });

  function hideChatWindow() {
    $('.ai-chat-window').fadeOut(150);
    chatOpen = false;
  }

  function addMessage(role, text) {
    const botAvatar = settings.botAvatar || '';
    const userAvatar = settings.userAvatar || '';
    let avatar = '';
    if (role === 'bot' && botAvatar) {
      avatar = `<img class="ai-chat-avatar" src="${botAvatar}" alt="Bot" />`;
    } else if (role === 'user' && userAvatar) {
      avatar = `<img class="ai-chat-avatar" src="${userAvatar}" alt="You" />`;
    }
    const msg = $(`
      <div class="ai-chat-message ${role}">
        ${avatar}
        <div class="ai-chat-bubble-content">${$('<div>').text(text).html()}</div>
      </div>
    `);
    $('.ai-chat-messages').append(msg);
    $('.ai-chat-messages').scrollTop($('.ai-chat-messages')[0].scrollHeight);
    history.push({role, text});
    sessionStorage.setItem('ai_chat_history', JSON.stringify(history));
  }

  function renderHistory() {
    $('.ai-chat-messages').empty();
    history.forEach(msg => addMessage(msg.role, msg.text));
  }

  function setTyping(isTyping) {
    if(isTyping) {
      $('.ai-chat-typing').show();
    } else {
      $('.ai-chat-typing').hide();
    }
  }

  async function sendMessage(userInput) {
    setTyping(true);
    const endpoint = aiChatAssistant.rest_url;
    const nonce = aiChatAssistant.nonce;
    const context = settings.context || '';
    const convHistory = history.slice(-6); // Last 6 messages for context
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-WP-Nonce': nonce
        },
        body: JSON.stringify({
          message: userInput,
          history: convHistory
        })
      });
      const data = await response.json();
      setTyping(false);
      if(data && data.reply) {
        addMessage('bot', data.reply);
      } else {
        addMessage('bot', 'Sorry, I could not get a response.');
      }
    } catch (e) {
      setTyping(false);
      addMessage('bot', 'Sorry, there was an error connecting to the AI.');
    }
  }

  $(document).ready(function(){
    // Restore history
    try {
      history = JSON.parse(sessionStorage.getItem('ai_chat_history')) || [];
    } catch(e) { history = []; }

    renderChatBubble();
    renderChatWindow();

    $('body').on('click', '.ai-chat-bubble', function(){
      showChatWindow();
    });
    $('body').on('click', '.ai-chat-close', function(){
      hideChatWindow();
    });
    $('body').on('submit', '.ai-chat-input-row', function(e){
      e.preventDefault();
      const input = $('.ai-chat-input');
      const val = input.val().trim();
      if(!val) return;
      addMessage('user', val);
      input.val('');
      sendMessage(val);
    });
    // Keyboard accessibility
    $('body').on('keydown', '.ai-chat-bubble', function(e){
      if(e.key === 'Enter' || e.key === ' '){
        showChatWindow();
      }
    });
  });
})(jQuery);
