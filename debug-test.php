<?php
/**
 * Debug test script for GPT Assistant Plugin
 * This file helps identify any remaining issues
 */

// Simulate WordPress environment for testing
if (!defined('ABSPATH')) {
    define('ABSPATH', '/path/to/wordpress/');
}

// Test 1: Check if main plugin file has syntax errors
echo "=== GPT Assistant Plugin Debug Test ===\n\n";

echo "1. Testing main plugin file syntax...\n";
$main_file = __DIR__ . '/elementor-ai-chat-assistant.php';
if (file_exists($main_file)) {
    $syntax_check = shell_exec("php -l $main_file 2>&1");
    if (strpos($syntax_check, 'No syntax errors') !== false) {
        echo "✓ Main plugin file syntax is OK\n";
    } else {
        echo "✗ Syntax error in main plugin file:\n$syntax_check\n";
    }
} else {
    echo "✗ Main plugin file not found\n";
}

// Test 2: Check include files
echo "\n2. Testing include files...\n";
$include_files = [
    'includes/helpers.php',
    'includes/class-gpt-assistant-settings.php',
    'includes/class-gpt-assistant-rest.php',
    'includes/class-gpt-assistant-shortcode.php',
    'widgets/class-gpt-assistant-widget.php'
];

foreach ($include_files as $file) {
    $full_path = __DIR__ . '/' . $file;
    if (file_exists($full_path)) {
        $syntax_check = shell_exec("php -l $full_path 2>&1");
        if (strpos($syntax_check, 'No syntax errors') !== false) {
            echo "✓ $file syntax is OK\n";
        } else {
            echo "✗ Syntax error in $file:\n$syntax_check\n";
        }
    } else {
        echo "✗ $file not found\n";
    }
}

// Test 3: Check asset files
echo "\n3. Testing asset files...\n";
$asset_files = [
    'assets/css/frontend.css',
    'assets/css/admin.css',
    'assets/js/frontend.js',
    'assets/js/admin.js'
];

foreach ($asset_files as $file) {
    $full_path = __DIR__ . '/' . $file;
    if (file_exists($full_path)) {
        echo "✓ $file exists\n";
    } else {
        echo "✗ $file not found\n";
    }
}

// Test 4: Check for common WordPress plugin issues
echo "\n4. Checking for common issues...\n";

// Check for option name consistency
$helpers_content = file_get_contents(__DIR__ . '/includes/helpers.php');
$settings_content = file_get_contents(__DIR__ . '/includes/class-gpt-assistant-settings.php');
$rest_content = file_get_contents(__DIR__ . '/includes/class-gpt-assistant-rest.php');

$option_name_pattern = '/elementor[_-].*?settings/';
preg_match_all($option_name_pattern, $helpers_content . $settings_content . $rest_content, $matches);

$unique_option_names = array_unique($matches[0]);
if (count($unique_option_names) === 1) {
    echo "✓ Option names are consistent: " . $unique_option_names[0] . "\n";
} else {
    echo "✗ Inconsistent option names found:\n";
    foreach ($unique_option_names as $name) {
        echo "  - $name\n";
    }
}

// Test 5: Check plugin header
echo "\n5. Checking plugin header...\n";
$main_content = file_get_contents($main_file);
if (strpos($main_content, 'Plugin Name:') !== false) {
    echo "✓ Plugin header found\n";
} else {
    echo "✗ Plugin header missing\n";
}

echo "\n=== Debug Test Complete ===\n";
echo "If all tests show ✓, the plugin should work without fatal errors.\n";
echo "If you see ✗, those are the issues that need to be fixed.\n";
