<?php
/**
 * Elementor Widget for GPT Assistant
 */
if ( ! defined( 'ABSPATH' ) ) exit;

use <PERSON>ementor\Widget_Base;
use <PERSON>ementor\Controls_Manager;

class GPT_Site_Assistant_Widget extends Widget_Base {
    public function get_name() {
        return 'ai_chat_assistant';
    }
    public function get_title() {
        return __('GPT Assistant', 'elementor-gpt-site-assistant');
    }
    public function get_icon() {
        return 'eicon-chat';
    }
    public function get_categories() {
        return [ 'general' ];
    }
    public function get_keywords() {
        return [ 'chat', 'ai', 'assistant', 'gpt', 'bot' ];
    }
    public function get_style_depends() {
        return [ 'elementor-gpt-site-assistant-frontend' ];
    }
    public function get_script_depends() {
        return [ 'elementor-gpt-site-assistant-frontend' ];
    }
    protected function register_controls() {
        $this->start_controls_section(
            'content_section',
            [ 'label' => __('Content', 'elementor-gpt-site-assistant'), 'tab' => Controls_Manager::TAB_CONTENT ]
        );
        $this->add_control('header_text', [
            'label' => __('Chat Header Text', 'elementor-gpt-site-assistant'),
            'type' => Controls_Manager::TEXT,
            'default' => __('How can I help?', 'elementor-gpt-site-assistant'),
        ]);
        $this->add_control('bot_name', [
            'label' => __('Bot Name', 'elementor-gpt-site-assistant'),
            'type' => Controls_Manager::TEXT,
            'default' => __('Support Bot', 'elementor-gpt-site-assistant'),
        ]);
        $this->add_control('bot_avatar', [
            'label' => __('Bot Avatar Image', 'elementor-gpt-site-assistant'),
            'type' => Controls_Manager::MEDIA,
            'default' => [ 'url' => '' ],
        ]);
        $this->add_control('greeting', [
            'label' => __('Initial Greeting Message', 'elementor-gpt-site-assistant'),
            'type' => Controls_Manager::TEXT,
            'default' => __('Hello! How can I assist you today?', 'elementor-gpt-site-assistant'),
        ]);
        $this->add_control('input_placeholder', [
            'label' => __('Input Placeholder Text', 'elementor-gpt-site-assistant'),
            'type' => Controls_Manager::TEXT,
            'default' => __('Type your question...', 'elementor-gpt-site-assistant'),
        ]);
        $this->end_controls_section();
        $this->start_controls_section(
            'style_section',
            [ 'label' => __('Style', 'elementor-gpt-site-assistant'), 'tab' => Controls_Manager::TAB_STYLE ]
        );
        $this->add_control('accent_color', [
            'label' => __('Accent Color', 'elementor-gpt-site-assistant'),
            'type' => Controls_Manager::COLOR,
            'default' => '#3f51b5',
            'selectors' => [
                '{{WRAPPER}} .ai-chat-bubble, {{WRAPPER}} .ai-chat-send-btn' => 'background-color: {{VALUE}};',
            ],
        ]);
        $this->add_control('header_bg', [
            'label' => __('Header Background', 'elementor-gpt-site-assistant'),
            'type' => Controls_Manager::COLOR,
            'default' => '#3f51b5',
            'selectors' => [
                '{{WRAPPER}} .ai-chat-header' => 'background-color: {{VALUE}};',
            ],
        ]);
        $this->add_control('header_color', [
            'label' => __('Header Text Color', 'elementor-gpt-site-assistant'),
            'type' => Controls_Manager::COLOR,
            'default' => '#fff',
            'selectors' => [
                '{{WRAPPER}} .ai-chat-header' => 'color: {{VALUE}};',
            ],
        ]);
        $this->add_control('window_width', [
            'label' => __('Chat Window Width', 'elementor-gpt-site-assistant'),
            'type' => Controls_Manager::NUMBER,
            'default' => 350,
            'selectors' => [
                '{{WRAPPER}} .ai-chat-window' => 'width: {{VALUE}}px;',
            ],
        ]);
        $this->add_control('window_height', [
            'label' => __('Chat Window Height', 'elementor-gpt-site-assistant'),
            'type' => Controls_Manager::NUMBER,
            'default' => 500,
            'selectors' => [
                '{{WRAPPER}} .ai-chat-window' => 'height: {{VALUE}}px;',
            ],
        ]);
        $this->end_controls_section();
    }
    protected function render() {
        if ( \Elementor\Plugin::$instance->editor->is_edit_mode() ) {
            echo '<div class="ai-chat-widget-placeholder"><span style="font-size:2em;opacity:0.6;">💬</span><br>';
            esc_html_e('GPT Assistant (preview)', 'elementor-gpt-site-assistant');
            echo '</div>';
        } else {
            echo '<div id="ai-chat-assistant-elementor-widget"></div>';
        }
    }
}
