<?php
/**
 * Admin settings page for Elementor GPT Assistant
 */

if ( ! defined( 'ABSPATH' ) ) exit;

class GPT_Site_Assistant_Plugin_Settings {
    private static $instance = null;

    public static function instance() {
        if ( self::$instance === null ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function __construct() {
        add_action( 'admin_init', [ $this, 'register_settings' ] );
    }

    public function add_admin_menu() {
        add_menu_page(
            __( 'GPT Assistant', 'elementor-gpt-site-assistant' ),
            __( 'GPT Assistant', 'elementor-gpt-site-assistant' ),
            'manage_options',
            'ai-chat-assistant',
            [ $this, 'settings_page' ],
            'dashicons-format-chat',
            56
        );
    }

    public function settings_page() {
        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'settings';
        $options = get_option('elementor_ai_chat_assistant_settings', []);
        ?>
        <div class="ai-chat-admin-wrap">
            <h1 class="ai-chat-admin-title"><?php esc_html_e('Elementor GPT Assistant', 'elementor-gpt-site-assistant'); ?></h1>
            <div class="ai-chat-admin-tabs">
                <button class="ai-chat-admin-tab<?php echo $active_tab === 'settings' ? ' active' : ''; ?>" data-tab="settings" onclick="location.href='?page=ai-chat-assistant&tab=settings'">
                    <?php esc_html_e('Settings', 'elementor-gpt-site-assistant'); ?>
                </button>
                <button class="ai-chat-admin-tab<?php echo $active_tab === 'howto' ? ' active' : ''; ?>" data-tab="howto" onclick="location.href='?page=ai-chat-assistant&tab=howto'">
                    <?php esc_html_e('How to Use', 'elementor-gpt-site-assistant'); ?>
                </button>
            </div>
            <div class="ai-chat-admin-section<?php echo $active_tab === 'settings' ? ' active' : ''; ?>" data-section="settings">
                <form method="post" action="options.php">
                    <?php
                    settings_fields('elementor_ai_chat_assistant_settings_group');
                    do_settings_sections('ai-chat-assistant');
                    submit_button(__('Save Settings', 'elementor-gpt-site-assistant'), 'primary ai-chat-admin-save');
                    ?>
                </form>
            </div>
            <div class="ai-chat-admin-section<?php echo $active_tab === 'howto' ? ' active' : ''; ?>" data-section="howto">
                <h2><?php esc_html_e('Getting Started', 'elementor-gpt-site-assistant'); ?></h2>
                <ol>
                    <li><?php esc_html_e('Get your API Key from your AI provider (e.g., OpenAI).', 'elementor-gpt-site-assistant'); ?></li>
                    <li><?php esc_html_e('Add your business info to the Knowledge Base tab.', 'elementor-gpt-site-assistant'); ?></li>
                    <li><?php esc_html_e('Add the chatbot to your page using the Elementor widget or the [ai_chat_assistant] shortcode.', 'elementor-gpt-site-assistant'); ?></li>
                </ol>
            </div>
        </div>
        <?php
    }

    public function register_settings() {
        register_setting(
            'elementor_ai_chat_assistant_settings_group',
            'elementor_ai_chat_assistant_settings',
            [ $this, 'sanitize_settings' ]
        );

        add_settings_section(
            'ai_chat_section',
            '',
            null,
            'ai-chat-assistant'
        );

        add_settings_field(
            'api_key',
            __('API Key', 'elementor-gpt-site-assistant'),
            [ $this, 'api_key_field' ],
            'ai-chat-assistant',
            'ai_chat_section'
        );
        add_settings_field(
            'context',
            __('Knowledge Base (Context)', 'elementor-gpt-site-assistant'),
            [ $this, 'context_field' ],
            'ai-chat-assistant',
            'ai_chat_section'
        );
        add_settings_field(
            'appearance',
            __('Default Appearance', 'elementor-gpt-site-assistant'),
            [ $this, 'appearance_field' ],
            'ai-chat-assistant',
            'ai_chat_section'
        );
    }

    public function sanitize_settings($input) {
        $output = [];
        $output['api_key'] = ai_chat_sanitize_api_key($input['api_key'] ?? '');
        $output['context'] = ai_chat_sanitize_context($input['context'] ?? '');
        $output['appearance'] = is_array($input['appearance']) ? array_map('sanitize_text_field', $input['appearance']) : [];
        return $output;
    }

    public function api_key_field() {
        $options = get_option('elementor_ai_chat_assistant_settings', []);
        $api_key = esc_attr($options['api_key'] ?? '');
        echo '<input class="ai-chat-admin-input" type="password" name="elementor_ai_chat_assistant_settings[api_key]" value="' . $api_key . '" placeholder="sk-..." />';
        echo '<div class="ai-chat-admin-desc"><a href="https://platform.openai.com/account/api-keys" target="_blank">' . esc_html__('Get your API key', 'elementor-gpt-site-assistant') . '</a></div>';
    }

    public function context_field() {
        $options = get_option('elementor_ai_chat_assistant_settings', []);
        $context = esc_textarea($options['context'] ?? '');
        echo '<textarea class="ai-chat-admin-textarea" name="elementor_ai_chat_assistant_settings[context]" placeholder="Paste your website content, FAQs, and business information here. The AI will use this text as its primary source of knowledge.">' . $context . '</textarea>';
        echo '<div class="ai-chat-admin-desc">' . esc_html__('Paste your website content, FAQs, and business information here. The AI will use this text as its primary source of knowledge.', 'elementor-gpt-site-assistant') . '</div>';
    }

    public function appearance_field() {
        $options = get_option('elementor_ai_chat_assistant_settings', []);
        $appearance = $options['appearance'] ?? [];
        $fields = [
            'accent' => __('Accent Color', 'elementor-gpt-site-assistant'),
            'header_text' => __('Header Text', 'elementor-gpt-site-assistant'),
            'bot_avatar' => __('Bot Avatar URL', 'elementor-gpt-site-assistant'),
        ];
        foreach ($fields as $key => $label) {
            $val = esc_attr($appearance[$key] ?? '');
            echo '<div class="ai-chat-admin-field">';
            echo '<label class="ai-chat-admin-label">' . $label . '</label>';
            echo '<input class="ai-chat-admin-input" type="text" name="elementor_ai_chat_assistant_settings[appearance][' . $key . ']" value="' . $val . '" />';
            echo '</div>';
        }
    }
}
