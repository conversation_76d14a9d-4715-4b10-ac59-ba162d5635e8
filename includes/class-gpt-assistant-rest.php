<?php
/**
 * REST API endpoint for Elementor AI Chat Assistant
 */
if ( ! defined( 'ABSPATH' ) ) exit;

class GPT_Site_Assistant_Plugin_REST {
    private static $instance = null;
    public static function instance() {
        if ( self::$instance === null ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    public function register_routes() {
        register_rest_route('ai-chat/v1', '/query', [
            'methods' => 'POST',
            'callback' => [ $this, 'handle_query' ],
            'permission_callback' => function () { return true; },
            'args' => [
                'message' => [ 'required' => true ],
                'history' => [ 'required' => false ],
            ]
        ]);
    }
    public function handle_query( $request ) {
        if ( ! wp_verify_nonce( $request->get_header('x-wp-nonce'), 'wp_rest' ) ) {
            return new WP_Error('invalid_nonce', __('Invalid nonce', 'elementor-gpt-site-assistant'), [ 'status' => 403 ]);
        }
        $params = $request->get_json_params();
        $message = sanitize_text_field( $params['message'] ?? '' );
        $history = isset($params['history']) && is_array($params['history']) ? array_slice($params['history'], -6) : [];
        $options = get_option('elementor_ai_chat_assistant_settings', []);
        $api_key = $options['api_key'] ?? '';
        $context = $options['context'] ?? '';
        if ( empty($api_key) ) {
            return [ 'reply' => __('AI API key not set.', 'elementor-gpt-site-assistant') ];
        }
        $prompt = $context . "\n";
        if ( ! empty($history) ) {
            foreach ($history as $msg) {
                $role = $msg['role'] === 'user' ? 'User' : 'Assistant';
                $prompt .= $role . ': ' . $msg['text'] . "\n";
            }
        }
        $prompt .= 'User: ' . $message . "\nAssistant:";
        $reply = $this->call_openai_api($api_key, $prompt);
        return [ 'reply' => $reply ];
    }
    private function call_openai_api($api_key, $prompt) {
        $endpoint = 'https://api.openai.com/v1/completions';
        $args = [
            'headers' => [
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type'  => 'application/json',
            ],
            'body' => json_encode([
                'model' => 'gpt-3.5-turbo-instruct',
                'prompt' => $prompt,
                'max_tokens' => 180,
                'temperature' => 0.7,
                'stop' => ["User:", "Assistant:"]
            ]),
            'timeout' => 20,
        ];
        $response = wp_remote_post($endpoint, $args);
        if ( is_wp_error($response) ) {
            return __('Error contacting AI service.', 'elementor-gpt-site-assistant');
        }
        $body = json_decode(wp_remote_retrieve_body($response), true);
        if ( isset($body['choices'][0]['text']) ) {
            return trim($body['choices'][0]['text']);
        }
        return __('No response from AI.', 'elementor-gpt-site-assistant');
    }
}
