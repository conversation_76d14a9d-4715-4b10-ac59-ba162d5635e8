<?php
/**
 * Helper functions for Elementor AI Chat Assistant
 */

if ( ! defined( 'ABSPATH' ) ) exit;

/**
 * Get plugin option
 */
function ai_chat_get_option( $key, $default = '' ) {
    $options = get_option( 'elementor_ai_chat_assistant_settings', [] );
    return isset( $options[$key] ) ? $options[$key] : $default;
}

/**
 * Sanitize API key (hidden in UI)
 */
function ai_chat_sanitize_api_key( $key ) {
    return sanitize_text_field( trim( $key ) );
}

/**
 * Sanitize context/knowledge base
 */
function ai_chat_sanitize_context( $context ) {
    return wp_kses_post( $context );
}
