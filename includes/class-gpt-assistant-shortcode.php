<?php
/**
 * Shortcode handler for Elementor-gpt-site-assistant
 */
if ( ! defined( 'ABSPATH' ) ) exit;

class GPT_Site_Assistant_Plugin_Shortcode {
    private static $instance = null;
    public static function instance() {
        if ( self::$instance === null ) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    public function render_shortcode( $atts = [], $content = null ) {
        ob_start();
        ?>
        <div id="ai-chat-assistant-shortcode-container"></div>
        <script>
        window.aiChatAssistantSettings = window.aiChatAssistantSettings || {};
        </script>
        <?php
        return ob_get_clean();
    }
}
